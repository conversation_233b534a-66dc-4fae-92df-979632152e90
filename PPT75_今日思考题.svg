<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .question { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; fill: #2c3e50; line-height: 1.4; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; fill: #e74c3c; }
      .end-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3498db; }
      .question-mark { font-family: 'Microsoft YaHei', sans-serif; font-size: 200px; font-weight: bold; fill: #3498db; opacity: 0.3; }
    </style>
    
    <!-- Gradient for question mark -->
    <linearGradient id="questionGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:0.2" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">今日思考题</text>
  
  <!-- Large question mark -->
  <text x="960" y="400" text-anchor="middle" class="question-mark">?</text>
  
  <!-- Question mark with gradient fill -->
  <text x="960" y="400" text-anchor="middle" font-family="Microsoft YaHei" font-size="200px" font-weight="bold" fill="url(#questionGrad)">?</text>
  
  <!-- Question content -->
  <g transform="translate(960,500)">
    <text x="0" y="0" text-anchor="middle" class="question">今天在营业厅里接待单个客户的"五步法"经验，</text>
    <text x="0" y="50" text-anchor="middle" class="question">明天在社区里，面对来来往往的人群，</text>
    <text x="0" y="100" text-anchor="middle" class="highlight">应该如何进行"变形"和"应用"？</text>
  </g>
  
  <!-- Thinking elements -->
  <g transform="translate(200,350)">
    <!-- Thought bubbles -->
    <circle cx="0" cy="0" r="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    <circle cx="50" cy="-20" r="20" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    <circle cx="80" cy="-35" r="12" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    
    <!-- Light bulb -->
    <g transform="translate(0,-50)">
      <circle cx="0" cy="0" r="25" fill="#f1c40f" opacity="0.7"/>
      <rect x="-8" y="20" width="16" height="15" fill="#34495e"/>
      <rect x="-10" y="35" width="20" height="5" fill="#34495e"/>
      <!-- Light rays -->
      <path d="M-35,-35 L-25,-25 M35,-35 L25,-25 M-35,35 L-25,25 M35,35 L25,25 M-45,0 L-35,0 M45,0 L35,0 M0,-45 L0,-35 M0,45 L0,35" stroke="#f1c40f" stroke-width="3"/>
    </g>
  </g>
  
  <g transform="translate(1720,350)">
    <!-- Thought bubbles -->
    <circle cx="0" cy="0" r="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    <circle cx="-50" cy="-20" r="20" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    <circle cx="-80" cy="-35" r="12" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    
    <!-- Gear -->
    <g transform="translate(0,-50)">
      <circle cx="0" cy="0" r="25" fill="#95a5a6" opacity="0.7"/>
      <circle cx="0" cy="0" r="15" fill="white"/>
      <!-- Gear teeth -->
      <rect x="-3" y="-30" width="6" height="10" fill="#95a5a6"/>
      <rect x="-3" y="20" width="6" height="10" fill="#95a5a6"/>
      <rect x="-30" y="-3" width="10" height="6" fill="#95a5a6"/>
      <rect x="20" y="-3" width="10" height="6" fill="#95a5a6"/>
    </g>
  </g>
  
  <!-- Bottom section -->
  <g transform="translate(0,750)">
    <!-- Separator line -->
    <path d="M200,0 L1720,0" stroke="#bdc3c7" stroke-width="2"/>
    
    <!-- End message -->
    <text x="960" y="80" text-anchor="middle" class="end-text">Day 2 课程结束，明天08:45，基地准时集合！</text>
  </g>
  
  <!-- Decorative elements -->
  <g transform="translate(100,750)">
    <!-- Clock icon -->
    <circle cx="30" cy="30" r="25" fill="none" stroke="#3498db" stroke-width="3"/>
    <path d="M30,10 L30,30 L45,30" stroke="#3498db" stroke-width="3"/>
    <text x="70" y="38" font-family="Microsoft YaHei" font-size="24" fill="#3498db">08:45</text>
  </g>
  
  <g transform="translate(1650,750)">
    <!-- Location pin -->
    <path d="M30,10 C35,5 45,5 50,10 C55,15 55,25 50,30 L40,50 L30,30 C25,25 25,15 30,10 Z" fill="#e74c3c"/>
    <circle cx="40" cy="20" r="5" fill="white"/>
    <text x="70" y="38" font-family="Microsoft YaHei" font-size="24" fill="#e74c3c">基地集合</text>
  </g>
  
  <!-- Motivational arrows -->
  <g transform="translate(400,650)">
    <path d="M0,0 Q50,-20 100,0" fill="none" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
    <text x="50" y="-35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#27ae60">思考</text>
  </g>
  
  <g transform="translate(1420,650)">
    <path d="M0,0 Q-50,-20 -100,0" fill="none" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead2)"/>
    <text x="-50" y="-35" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#27ae60">应用</text>
  </g>
  
  <!-- Arrow markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="1" refY="3.5" orient="auto">
      <polygon points="10 0, 0 3.5, 10 7" fill="#27ae60"/>
    </marker>
  </defs>
</svg>
