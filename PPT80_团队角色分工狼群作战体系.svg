<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .role-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #2c3e50; }
      .role-subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #e74c3c; }
      .task-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #34495e; }
      .requirement-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 22px; fill: #7f8c8d; }
      .role-bg { fill: #f8f9fa; stroke: #3498db; stroke-width: 3; }
      .wolf-color { fill: #34495e; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">团队角色分工："狼群"作战体系</text>
  
  <!-- Wolf pack illustration -->
  <g transform="translate(960,200)" opacity="0.4">
    <!-- Alpha wolf (center) -->
    <ellipse cx="0" cy="0" rx="25" ry="15" class="wolf-color"/>
    <ellipse cx="-15" cy="-5" rx="8" ry="6" class="wolf-color"/>
    <path d="M-20,-8 L-25,-12 M-20,-2 L-25,2" stroke="#34495e" stroke-width="2"/>
    
    <!-- Pack wolves -->
    <ellipse cx="-60" cy="20" rx="20" ry="12" class="wolf-color"/>
    <ellipse cx="-45" cy="15" rx="6" ry="5" class="wolf-color"/>
    
    <ellipse cx="60" cy="15" rx="20" ry="12" class="wolf-color"/>
    <ellipse cx="45" cy="10" rx="6" ry="5" class="wolf-color"/>
    
    <ellipse cx="0" cy="40" rx="18" ry="10" class="wolf-color"/>
    <ellipse cx="-10" cy="35" rx="5" ry="4" class="wolf-color"/>
  </g>
  
  <!-- Role 1: 破冰手/引流员 -->
  <g transform="translate(200,350)">
    <rect x="0" y="0" width="450" height="280" rx="20" class="role-bg"/>
    
    <!-- Role number and icon -->
    <circle cx="60" cy="60" r="30" fill="#3498db"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">1</text>
    
    <!-- Ice breaking icon -->
    <g transform="translate(350,40)">
      <path d="M0,0 L20,20 M20,0 L0,20" stroke="#3498db" stroke-width="4"/>
      <circle cx="10" cy="10" r="15" fill="none" stroke="#3498db" stroke-width="3" stroke-dasharray="5,5"/>
    </g>
    
    <text x="120" y="45" class="role-title">破冰手 / 引流员</text>
    <text x="120" y="75" class="role-subtitle">(2人)</text>
    
    <text x="30" y="120" class="task-text">任务：主动出击，用话术和礼品吸引居民关注，</text>
    <text x="30" y="150" class="task-text">引导至展台。</text>
    
    <text x="30" y="190" class="requirement-text">要求：胆大、心细、脸皮厚、亲和力强。</text>
    
    <!-- People icons -->
    <g transform="translate(30,220)">
      <circle cx="15" cy="15" r="12" fill="#3498db" opacity="0.7"/>
      <rect x="8" y="27" width="14" height="25" fill="#3498db" opacity="0.7"/>
      
      <circle cx="45" cy="15" r="12" fill="#3498db" opacity="0.7"/>
      <rect x="38" y="27" width="14" height="25" fill="#3498db" opacity="0.7"/>
    </g>
  </g>
  
  <!-- Role 2: 主讲师/演示员 -->
  <g transform="translate(735,350)">
    <rect x="0" y="0" width="450" height="280" rx="20" class="role-bg"/>
    
    <!-- Role number and icon -->
    <circle cx="60" cy="60" r="30" fill="#27ae60"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">2</text>
    
    <!-- Presentation icon -->
    <g transform="translate(350,40)">
      <rect x="0" y="0" width="30" height="20" fill="none" stroke="#27ae60" stroke-width="3"/>
      <path d="M35,10 L45,5 L45,15 Z" fill="#27ae60"/>
      <circle cx="50" cy="10" r="8" fill="none" stroke="#27ae60" stroke-width="2"/>
    </g>
    
    <text x="120" y="45" class="role-title">主讲师 / 演示员</text>
    <text x="120" y="75" class="role-subtitle">(1人)</text>
    
    <text x="30" y="120" class="task-text">任务：负责产品演示和价值讲解，</text>
    <text x="30" y="150" class="task-text">制造"哇"的惊叹时刻。</text>
    
    <text x="30" y="190" class="requirement-text">要求：口才好、逻辑清、熟悉产品、富有激情。</text>
    
    <!-- Speaker icon -->
    <g transform="translate(30,220)">
      <circle cx="25" cy="15" r="15" fill="#27ae60" opacity="0.7"/>
      <rect x="18" y="30" width="14" height="25" fill="#27ae60" opacity="0.7"/>
      <!-- Microphone -->
      <rect x="40" y="10" width="4" height="15" fill="#34495e"/>
      <circle cx="42" cy="8" r="3" fill="#34495e"/>
    </g>
  </g>
  
  <!-- Role 3: 信息登记员/转化手 -->
  <g transform="translate(1270,350)">
    <rect x="0" y="0" width="450" height="280" rx="20" class="role-bg"/>
    
    <!-- Role number and icon -->
    <circle cx="60" cy="60" r="30" fill="#e74c3c"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="white">3</text>
    
    <!-- Registration icon -->
    <g transform="translate(350,40)">
      <rect x="0" y="0" width="25" height="35" rx="3" fill="none" stroke="#e74c3c" stroke-width="3"/>
      <path d="M5,10 L20,10 M5,18 L20,18 M5,26 L15,26" stroke="#e74c3c" stroke-width="2"/>
      <circle cx="30" cy="15" r="5" fill="#e74c3c"/>
    </g>
    
    <text x="120" y="45" class="role-title">信息登记员 / 转化手</text>
    <text x="120" y="75" class="role-subtitle">(1人)</text>
    
    <text x="30" y="120" class="task-text">任务：引导客户登记信息，进行初步需求沟通</text>
    <text x="30" y="150" class="task-text">和意向判断，力争加微信或预约。</text>
    
    <text x="30" y="190" class="requirement-text">要求：有耐心、善于倾听、目标感强。</text>
    
    <!-- Registration person icon -->
    <g transform="translate(30,220)">
      <circle cx="25" cy="15" r="15" fill="#e74c3c" opacity="0.7"/>
      <rect x="18" y="30" width="14" height="25" fill="#e74c3c" opacity="0.7"/>
      <!-- Clipboard -->
      <rect x="40" y="8" width="12" height="16" rx="2" fill="none" stroke="#34495e" stroke-width="2"/>
      <path d="M43,12 L49,12 M43,16 L49,16 M43,20 L47,20" stroke="#34495e" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Team coordination arrows -->
  <g stroke="#95a5a6" stroke-width="3" opacity="0.6">
    <!-- From role 1 to role 2 -->
    <path d="M650,490 L735,490" marker-end="url(#arrowhead)"/>
    <!-- From role 2 to role 3 -->
    <path d="M1185,490 L1270,490" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Workflow labels -->
  <text x="692" y="480" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#95a5a6">引流</text>
  <text x="1227" y="480" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#95a5a6">转化</text>
  
  <!-- Bottom section: Team synergy -->
  <g transform="translate(0,700)">
    <!-- Synergy message -->
    <rect x="560" y="50" width="800" height="100" rx="20" fill="#fff" stroke="#2c3e50" stroke-width="3"/>
    <text x="960" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2c3e50">团队协作 = 最大化战斗力</text>
    <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#7f8c8d">侦察兵 + 主炮手 + 终结者 = 完美狼群</text>
  </g>
  
  <!-- Success indicators -->
  <g transform="translate(200,750)">
    <!-- Team strength meter -->
    <rect x="0" y="0" width="100" height="20" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
    <rect x="0" y="0" width="85" height="20" fill="#27ae60"/>
    <text x="120" y="15" font-family="Microsoft YaHei" font-size="20" fill="#27ae60">团队战力：85%</text>
  </g>
  
  <g transform="translate(1520,750)">
    <!-- Coordination indicator -->
    <circle cx="15" cy="10" r="12" fill="none" stroke="#3498db" stroke-width="3"/>
    <path d="M8,10 L12,14 L22,4" fill="none" stroke="#3498db" stroke-width="3"/>
    <text x="40" y="15" font-family="Microsoft YaHei" font-size="20" fill="#3498db">协作就绪</text>
  </g>
  
  <!-- Arrow marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#95a5a6"/>
    </marker>
  </defs>
</svg>
