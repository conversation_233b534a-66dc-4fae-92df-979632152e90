<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .principle-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #e74c3c; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .example-wrong { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #e74c3c; }
      .example-right { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #27ae60; }
      .dialog-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
      .number { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3498db; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">集体诊断：建设性反馈三原则</text>
  
  <!-- Principle 1 -->
  <g transform="translate(100,250)">
    <!-- Dialog box -->
    <rect x="0" y="0" width="500" height="220" rx="20" class="dialog-bg"/>
    <circle cx="50" cy="50" r="25" fill="#3498db"/>
    <text x="50" y="60" text-anchor="middle" class="number">1</text>
    
    <text x="100" y="50" class="principle-title">只谈行为，不谈对错</text>
    
    <text x="30" y="100" class="example-wrong">不说："你这个人太急了。"</text>
    
    <text x="30" y="140" class="example-right">要说："我注意到，在客户还在</text>
    <text x="30" y="170" class="example-right">描述问题时，你就开始介绍产品了。"</text>
  </g>
  
  <!-- Principle 2 -->
  <g transform="translate(710,250)">
    <!-- Dialog box -->
    <rect x="0" y="0" width="500" height="220" rx="20" class="dialog-bg"/>
    <circle cx="50" cy="50" r="25" fill="#3498db"/>
    <text x="50" y="60" text-anchor="middle" class="number">2</text>
    
    <text x="100" y="50" class="principle-title">先说优点，再说不足</text>
    
    <text x="30" y="100" class="example-right">先说："你刚才的开场白非常亲切，</text>
    <text x="30" y="125" class="example-right">瞬间就拉近了距离，这一点特别棒。"</text>
    
    <text x="30" y="165" class="example-right">再说："如果在这个基础上，你能多问</text>
    <text x="30" y="190" class="example-right">一个I问题，效果可能会更好。"</text>
  </g>
  
  <!-- Principle 3 -->
  <g transform="translate(1320,250)">
    <!-- Dialog box -->
    <rect x="0" y="0" width="500" height="220" rx="20" class="dialog-bg"/>
    <circle cx="50" cy="50" r="25" fill="#3498db"/>
    <text x="50" y="60" text-anchor="middle" class="number">3</text>
    
    <text x="100" y="50" class="principle-title">建议必须具体</text>
    
    <text x="30" y="100" class="example-wrong">不说："你应该更主动一点。"</text>
    
    <text x="30" y="140" class="example-right">要说："在客户说出那句话后，</text>
    <text x="30" y="170" class="example-right">我建议你紧接着可以这样说……"</text>
  </g>
  
  <!-- Bottom section with connecting elements -->
  <g transform="translate(0,550)">
    <!-- Curved connecting line -->
    <path d="M350,50 Q960,0 1570,50" fill="none" stroke="#3498db" stroke-width="4"/>
    
    <!-- Central message -->
    <rect x="660" y="100" width="600" height="120" rx="20" fill="#f8f9fa" stroke="#3498db" stroke-width="3"/>
    <text x="960" y="140" text-anchor="middle" class="content">确保我们的复盘</text>
    <text x="960" y="180" text-anchor="middle" class="content">高效 · 积极 · 真正有帮助</text>
  </g>
  
  <!-- Speech bubble icons -->
  <g transform="translate(200,600)">
    <ellipse cx="0" cy="0" rx="40" ry="25" fill="#3498db" opacity="0.3"/>
    <ellipse cx="0" cy="0" rx="30" ry="18" fill="#3498db" opacity="0.5"/>
    <ellipse cx="0" cy="0" rx="20" ry="12" fill="#3498db"/>
  </g>
  
  <g transform="translate(960,600)">
    <ellipse cx="0" cy="0" rx="40" ry="25" fill="#27ae60" opacity="0.3"/>
    <ellipse cx="0" cy="0" rx="30" ry="18" fill="#27ae60" opacity="0.5"/>
    <ellipse cx="0" cy="0" rx="20" ry="12" fill="#27ae60"/>
  </g>
  
  <g transform="translate(1720,600)">
    <ellipse cx="0" cy="0" rx="40" ry="25" fill="#e74c3c" opacity="0.3"/>
    <ellipse cx="0" cy="0" rx="30" ry="18" fill="#e74c3c" opacity="0.5"/>
    <ellipse cx="0" cy="0" rx="20" ry="12" fill="#e74c3c"/>
  </g>
</svg>
