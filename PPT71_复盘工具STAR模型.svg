<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #34495e; }
      .content { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #2c3e50; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .star-bg { fill: #3498db; opacity: 0.1; }
      .star-border { fill: none; stroke: #3498db; stroke-width: 3; }
      .star-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3498db; }
      .detail-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #34495e; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">复盘工具："录音 + STAR模型"</text>
  
  <!-- STAR Model Structure -->
  <g transform="translate(200,250)">
    <!-- S - Situation -->
    <circle cx="200" cy="150" r="80" class="star-bg"/>
    <circle cx="200" cy="150" r="80" class="star-border"/>
    <text x="200" y="160" text-anchor="middle" class="star-text">S</text>
    <text x="200" y="260" text-anchor="middle" class="subtitle">Situation</text>
    <text x="200" y="300" text-anchor="middle" class="content">(情景)</text>
    <text x="200" y="340" text-anchor="middle" class="detail-text">客户是谁？</text>
    <text x="200" y="370" text-anchor="middle" class="detail-text">在什么情况下进店？</text>
    
    <!-- T - Task -->
    <circle cx="600" cy="150" r="80" class="star-bg"/>
    <circle cx="600" cy="150" r="80" class="star-border"/>
    <text x="600" y="160" text-anchor="middle" class="star-text">T</text>
    <text x="600" y="260" text-anchor="middle" class="subtitle">Task</text>
    <text x="600" y="300" text-anchor="middle" class="content">(任务)</text>
    <text x="600" y="340" text-anchor="middle" class="detail-text">我当时的目标是什么？</text>
    <text x="600" y="370" text-anchor="middle" class="detail-text">(如：销售一套安防套餐)</text>
    
    <!-- A - Action -->
    <circle cx="200" cy="450" r="80" class="star-bg"/>
    <circle cx="200" cy="450" r="80" class="star-border"/>
    <text x="200" y="460" text-anchor="middle" class="star-text">A</text>
    <text x="200" y="560" text-anchor="middle" class="subtitle">Action</text>
    <text x="200" y="600" text-anchor="middle" class="content">(行动)</text>
    <text x="200" y="640" text-anchor="middle" class="detail-text">我具体采取了哪些行动？</text>
    <text x="200" y="670" text-anchor="middle" class="detail-text">(如：我如何提问、如何演示)</text>
    
    <!-- R - Result -->
    <circle cx="600" cy="450" r="80" class="star-bg"/>
    <circle cx="600" cy="450" r="80" class="star-border"/>
    <text x="600" y="460" text-anchor="middle" class="star-text">R</text>
    <text x="600" y="560" text-anchor="middle" class="subtitle">Result</text>
    <text x="600" y="600" text-anchor="middle" class="content">(结果)</text>
    <text x="600" y="640" text-anchor="middle" class="detail-text">最终的结果是什么？为什么？</text>
    <text x="600" y="670" text-anchor="middle" class="detail-text">如果重来，我会如何改进？</text>
    
    <!-- Connecting lines -->
    <path d="M280,150 L520,150" fill="none" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M600,230 L600,370" fill="none" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M520,450 L280,450" fill="none" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
    <path d="M200,370 L200,230" fill="none" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Recording device icon and text -->
  <g transform="translate(1200,350)">
    <!-- Microphone icon -->
    <rect x="50" y="100" width="60" height="120" rx="30" fill="#e74c3c"/>
    <rect x="65" y="240" width="30" height="40" fill="#34495e"/>
    <rect x="40" y="270" width="80" height="20" rx="10" fill="#34495e"/>
    <circle cx="80" cy="80" r="15" fill="#c0392b"/>
    
    <text x="80" y="350" text-anchor="middle" class="highlight">回放关键对话录音，</text>
    <text x="80" y="390" text-anchor="middle" class="highlight">进行微观分析。</text>
  </g>
  
  <!-- Arrow markers -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
    </marker>
  </defs>
</svg>
