<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .ring-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #2c3e50; }
      .ring-content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #34495e; }
      .highlight { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #e74c3c; }
      .warning { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #e74c3c; }
      .outer-ring { fill: #ecf0f1; stroke: #95a5a6; stroke-width: 4; }
      .middle-ring { fill: #d5dbdb; stroke: #7f8c8d; stroke-width: 4; }
      .inner-ring { fill: #e74c3c; stroke: #c0392b; stroke-width: 4; opacity: 0.9; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">使命目标：从线索到邀约</text>
  
  <!-- Target rings -->
  <g transform="translate(960,500)">
    <!-- Outer ring (Basic Goal) -->
    <circle cx="0" cy="0" r="200" class="outer-ring"/>
    <text x="0" y="-160" text-anchor="middle" class="ring-title">外环（基础目标）</text>
    <text x="0" y="-130" text-anchor="middle" class="ring-content">策划一份专业、可执行的</text>
    <text x="0" y="-105" text-anchor="middle" class="ring-content">《活动策划书》。</text>
    
    <!-- Middle ring (Core Goal) -->
    <circle cx="0" cy="0" r="130" class="middle-ring"/>
    <text x="0" y="-80" text-anchor="middle" class="ring-title">中环（核心目标）</text>
    <text x="0" y="-50" text-anchor="middle" class="ring-content">团队合作，收集有效潜客信息</text>
    <text x="0" y="-25" text-anchor="middle" class="highlight">≥ 30条</text>
    
    <!-- Inner ring (Decisive Goal) -->
    <circle cx="0" cy="0" r="70" class="inner-ring"/>
    <text x="0" y="-10" text-anchor="middle" class="ring-title" fill="white">靶心（决胜目标）</text>
    <text x="0" y="15" text-anchor="middle" class="ring-content" fill="white">成功电话邀约 A类客户</text>
    <text x="0" y="40" text-anchor="middle" class="highlight" fill="white">≥ 2位</text>
    <text x="0" y="65" text-anchor="middle" class="ring-content" fill="white" font-size="20px">获得明日"上门服务"资格</text>
  </g>
  
  <!-- Warning message -->
  <g transform="translate(1300,500)">
    <rect x="0" y="-50" width="500" height="100" rx="15" fill="#fff5f5" stroke="#e74c3c" stroke-width="3"/>
    <text x="250" y="-10" text-anchor="middle" class="warning">今天的邀约成功数，</text>
    <text x="250" y="25" text-anchor="middle" class="warning">决定了明天你是否有机会签大单！</text>
  </g>
  
  <!-- Goal progression arrows -->
  <g transform="translate(960,500)">
    <!-- Arrow from outer to middle -->
    <path d="M-140,0 L-100,0 M-115,-10 L-100,0 L-115,10" fill="none" stroke="#27ae60" stroke-width="4"/>
    
    <!-- Arrow from middle to inner -->
    <path d="M-90,0 L-50,0 M-65,-8 L-50,0 L-65,8" fill="none" stroke="#27ae60" stroke-width="4"/>
  </g>
  
  <!-- Achievement indicators -->
  <g transform="translate(200,300)">
    <!-- Planning icon -->
    <rect x="0" y="0" width="60" height="80" rx="5" fill="#3498db" opacity="0.7"/>
    <rect x="10" y="15" width="40" height="3" fill="white"/>
    <rect x="10" y="25" width="35" height="2" fill="white"/>
    <rect x="10" y="32" width="38" height="2" fill="white"/>
    <rect x="10" y="45" width="40" height="3" fill="white"/>
    <rect x="10" y="55" width="30" height="2" fill="white"/>
    <text x="30" y="110" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#3498db">专业策划</text>
  </g>
  
  <g transform="translate(200,500)">
    <!-- Data collection icon -->
    <rect x="10" y="10" width="40" height="60" rx="5" fill="#27ae60" opacity="0.7"/>
    <rect x="15" y="20" width="10" height="15" fill="white"/>
    <rect x="30" y="25" width="10" height="10" fill="white"/>
    <rect x="15" y="40" width="15" height="8" fill="white"/>
    <rect x="35" y="45" width="10" height="3" fill="white"/>
    <rect x="15" y="55" width="30" height="3" fill="white"/>
    <text x="30" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#27ae60">线索收集</text>
  </g>
  
  <g transform="translate(200,700)">
    <!-- Phone call icon -->
    <path d="M20,10 Q10,10 10,20 L10,50 Q10,60 20,60 L40,60 Q50,60 50,50 L50,20 Q50,10 40,10 Z" fill="#e74c3c" opacity="0.7"/>
    <rect x="15" y="20" width="20" height="3" fill="white"/>
    <rect x="15" y="30" width="25" height="3" fill="white"/>
    <rect x="15" y="40" width="20" height="3" fill="white"/>
    <text x="30" y="90" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#e74c3c">电话邀约</text>
  </g>
  
  <!-- Success metrics -->
  <g transform="translate(1500,750)">
    <!-- Success rate indicator -->
    <circle cx="30" cy="30" r="25" fill="none" stroke="#27ae60" stroke-width="4"/>
    <path d="M15,30 L25,40 L45,20" fill="none" stroke="#27ae60" stroke-width="4"/>
    <text x="70" y="25" font-family="Microsoft YaHei" font-size="24" fill="#27ae60">成功标准</text>
    <text x="70" y="50" font-family="Microsoft YaHei" font-size="20" fill="#2c3e50">质量 > 数量</text>
  </g>
  
  <!-- Pressure and motivation -->
  <g transform="translate(100,750)">
    <!-- Pressure gauge -->
    <path d="M30,50 A25,25 0 0,1 80,25" fill="none" stroke="#e74c3c" stroke-width="4"/>
    <path d="M30,50 A25,25 0 0,1 55,5" fill="none" stroke="#f39c12" stroke-width="4"/>
    <circle cx="55" cy="30" r="3" fill="#e74c3c"/>
    <text x="100" y="25" font-family="Microsoft YaHei" font-size="24" fill="#e74c3c">压力</text>
    <text x="100" y="50" font-family="Microsoft YaHei" font-size="24" fill="#27ae60">动力</text>
  </g>
  
  <!-- Connecting elements -->
  <path d="M400,400 Q600,350 800,400" fill="none" stroke="#3498db" stroke-width="3" opacity="0.5"/>
  <path d="M400,600 Q600,550 800,600" fill="none" stroke="#27ae60" stroke-width="3" opacity="0.5"/>
  <path d="M400,800 Q600,750 800,800" fill="none" stroke="#e74c3c" stroke-width="3" opacity="0.5"/>
</svg>
