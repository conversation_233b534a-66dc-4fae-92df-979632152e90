<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .main-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #e74c3c; }
      .english { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; fill: #7f8c8d; font-style: italic; }
      .community-bg { fill: #ecf0f1; opacity: 0.4; }
    </style>
    
    <!-- Gradient backgrounds -->
    <linearGradient id="skyGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#4682B4;stop-opacity:0.1" />
    </linearGradient>
    
    <radialGradient id="sunGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Sky background -->
  <rect x="0" y="0" width="1920" height="600" fill="url(#skyGrad)"/>
  
  <!-- Sun -->
  <circle cx="1600" cy="200" r="80" fill="url(#sunGrad)"/>
  
  <!-- Community aerial view illustration -->
  <g transform="translate(0,300)" opacity="0.6">
    <!-- Roads -->
    <rect x="0" y="200" width="1920" height="40" fill="#95a5a6"/>
    <rect x="400" y="0" width="40" height="500" fill="#95a5a6"/>
    <rect x="800" y="0" width="40" height="500" fill="#95a5a6"/>
    <rect x="1200" y="0" width="40" height="500" fill="#95a5a6"/>
    
    <!-- Road markings -->
    <rect x="0" y="215" width="1920" height="10" fill="#f1c40f" opacity="0.8"/>
    <rect x="415" y="0" width="10" height="500" fill="#f1c40f" opacity="0.8"/>
    <rect x="815" y="0" width="10" height="500" fill="#f1c40f" opacity="0.8"/>
    <rect x="1215" y="0" width="10" height="500" fill="#f1c40f" opacity="0.8"/>
    
    <!-- Building blocks -->
    <g fill="#34495e" opacity="0.3">
      <!-- Block 1 -->
      <rect x="50" y="50" width="300" height="120" rx="10"/>
      <rect x="80" y="80" width="60" height="60" fill="#3498db" opacity="0.5"/>
      <rect x="160" y="80" width="60" height="60" fill="#3498db" opacity="0.5"/>
      <rect x="240" y="80" width="60" height="60" fill="#3498db" opacity="0.5"/>
      
      <!-- Block 2 -->
      <rect x="480" y="30" width="280" height="140" rx="10"/>
      <rect x="510" y="60" width="50" height="50" fill="#27ae60" opacity="0.5"/>
      <rect x="580" y="60" width="50" height="50" fill="#27ae60" opacity="0.5"/>
      <rect x="650" y="60" width="50" height="50" fill="#27ae60" opacity="0.5"/>
      <rect x="720" y="60" width="50" height="50" fill="#27ae60" opacity="0.5"/>
      
      <!-- Block 3 -->
      <rect x="880" y="60" width="280" height="110" rx="10"/>
      <rect x="910" y="90" width="50" height="50" fill="#e74c3c" opacity="0.5"/>
      <rect x="980" y="90" width="50" height="50" fill="#e74c3c" opacity="0.5"/>
      <rect x="1050" y="90" width="50" height="50" fill="#e74c3c" opacity="0.5"/>
      <rect x="1120" y="90" width="50" height="50" fill="#e74c3c" opacity="0.5"/>
      
      <!-- Block 4 -->
      <rect x="1280" y="40" width="300" height="130" rx="10"/>
      <rect x="1310" y="70" width="60" height="60" fill="#9b59b6" opacity="0.5"/>
      <rect x="1390" y="70" width="60" height="60" fill="#9b59b6" opacity="0.5"/>
      <rect x="1470" y="70" width="60" height="60" fill="#9b59b6" opacity="0.5"/>
      
      <!-- Block 5 -->
      <rect x="50" y="280" width="300" height="120" rx="10"/>
      <rect x="480" y="300" width="280" height="100" rx="10"/>
      <rect x="880" y="290" width="280" height="110" rx="10"/>
      <rect x="1280" y="280" width="300" height="120" rx="10"/>
    </g>
    
    <!-- Green spaces -->
    <g fill="#27ae60" opacity="0.3">
      <ellipse cx="200" cy="250" rx="80" ry="40"/>
      <ellipse cx="600" cy="250" rx="70" ry="35"/>
      <ellipse cx="1000" cy="250" rx="75" ry="38"/>
      <ellipse cx="1400" cy="250" rx="85" ry="42"/>
    </g>
  </g>
  
  <!-- Main content overlay -->
  <g transform="translate(0,0)">
    <!-- Semi-transparent overlay for text readability -->
    <rect x="0" y="400" width="1920" height="400" fill="white" opacity="0.9"/>
    
    <!-- Main title -->
    <text x="960" y="500" text-anchor="middle" class="main-title">第三天：社区营销 · 主动破局</text>
    
    <!-- Subtitle -->
    <text x="960" y="580" text-anchor="middle" class="subtitle">从"等客上门"到"主动出击"，王牌，从不等待！</text>
    
    <!-- English subtitle -->
    <text x="960" y="640" text-anchor="middle" class="english">Day 3: Community Marketing · Proactive Breakthrough</text>
  </g>
  
  <!-- Decorative elements -->
  <g transform="translate(200,720)">
    <!-- Forward arrow -->
    <path d="M0,20 L60,20 M45,5 L60,20 L45,35" fill="none" stroke="#e74c3c" stroke-width="6"/>
    <text x="80" y="30" font-family="Microsoft YaHei" font-size="28" fill="#e74c3c" font-weight="bold">主动出击</text>
  </g>
  
  <g transform="translate(1520,720)">
    <!-- Target with arrow -->
    <circle cx="25" cy="20" r="20" fill="none" stroke="#27ae60" stroke-width="4"/>
    <circle cx="25" cy="20" r="12" fill="none" stroke="#27ae60" stroke-width="3"/>
    <circle cx="25" cy="20" r="6" fill="#27ae60"/>
    <path d="M5,0 L25,20 L5,40" fill="none" stroke="#27ae60" stroke-width="3"/>
    <text x="60" y="30" font-family="Microsoft YaHei" font-size="28" fill="#27ae60" font-weight="bold">精准破局</text>
  </g>
  
  <!-- Community activity indicators -->
  <g transform="translate(300,200)" opacity="0.7">
    <!-- People gathering -->
    <g fill="#2c3e50">
      <circle cx="0" cy="0" r="8"/>
      <circle cx="20" cy="5" r="8"/>
      <circle cx="40" cy="-3" r="8"/>
      <circle cx="60" cy="2" r="8"/>
    </g>
    <text x="30" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#2c3e50">社区活动</text>
  </g>
  
  <g transform="translate(1400,180)" opacity="0.7">
    <!-- Marketing booth -->
    <rect x="0" y="0" width="60" height="30" fill="#3498db"/>
    <rect x="10" y="-20" width="40" height="20" fill="#e74c3c"/>
    <text x="30" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#2c3e50">营销展台</text>
  </g>
  
  <!-- Motivational wave -->
  <path d="M0,900 Q480,850 960,900 T1920,900" fill="none" stroke="#3498db" stroke-width="4" opacity="0.6"/>
  <path d="M0,920 Q480,870 960,920 T1920,920" fill="none" stroke="#e74c3c" stroke-width="4" opacity="0.6"/>
</svg>
