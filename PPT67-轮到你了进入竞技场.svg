<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 竞技场背景效果 -->
  <defs>
    <radialGradient id="arenaGradient" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:0.3"/>
      <stop offset="50%" style="stop-color:#e9ecef;stop-opacity:0.6"/>
      <stop offset="100%" style="stop-color:#0066cc;stop-opacity:0.1"/>
    </radialGradient>
    
    <!-- 动感弧线 -->
    <path id="dynamicArc" d="M 0,300 Q 400,200 800,300 T 1600,300" stroke="#0066cc" stroke-width="4" fill="none" opacity="0.4"/>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1920" height="1080" fill="url(#arenaGradient)"/>
  
  <!-- 动感装饰弧线 -->
  <use href="#dynamicArc" transform="translate(160, 100)"/>
  <use href="#dynamicArc" transform="translate(160, 200) scale(1.2, 1)"/>
  <use href="#dynamicArc" transform="translate(160, 600) scale(1, -1)"/>
  <use href="#dynamicArc" transform="translate(160, 700) scale(1.2, -1)"/>
  
  <!-- 竞技场图形元素 -->
  <ellipse cx="960" cy="540" rx="600" ry="200" fill="none" stroke="#0066cc" stroke-width="6" opacity="0.3"/>
  <ellipse cx="960" cy="540" rx="400" ry="130" fill="none" stroke="#0066cc" stroke-width="4" opacity="0.5"/>
  <ellipse cx="960" cy="540" rx="200" ry="65" fill="none" stroke="#0066cc" stroke-width="3" opacity="0.7"/>
  
  <!-- 标题 -->
  <text x="960" y="200" font-family="Microsoft YaHei" font-size="88" font-weight="bold" fill="#1a1a1a" text-anchor="middle">轮到你了：进入竞技场！</text>
  
  <!-- 副标题 -->
  <text x="960" y="280" font-family="Microsoft YaHei" font-size="42" fill="#666666" text-anchor="middle">独立客户接待与转化 (Independent Customer Reception & Conversion)</text>
  
  <!-- 中央大字 -->
  <rect x="300" y="450" width="1320" height="120" rx="20" fill="#dc3545" opacity="0.1"/>
  <text x="960" y="530" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="#dc3545" text-anchor="middle">你的每一次接触，都是一次实战考核！</text>
  
  <!-- 挑战感装饰元素 -->
  <polygon points="200,800 250,750 300,800 250,850" fill="#0066cc" opacity="0.3"/>
  <polygon points="1620,800 1670,750 1720,800 1670,850" fill="#0066cc" opacity="0.3"/>
  
  <!-- 底部激励文字 -->
  <path d="M 400,750 Q 960,800 1520,750" stroke="#0066cc" stroke-width="4" fill="none" opacity="0.6"/>
  <text x="960" y="900" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#0066cc" text-anchor="middle">勇气 + 专业 = 胜利</text>
</svg>
