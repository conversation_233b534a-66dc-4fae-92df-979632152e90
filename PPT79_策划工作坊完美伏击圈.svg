<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #e74c3c; }
      .tool-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3498db; }
      .element-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #2c3e50; }
      .element-content { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #34495e; }
      .center-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .element-bg { fill: #f8f9fa; stroke: #3498db; stroke-width: 3; }
      .center-bg { fill: #e74c3c; opacity: 0.9; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="100" text-anchor="middle" class="title">策划工作坊：设计我们的"完美伏击圈"</text>
  
  <!-- Subtitle -->
  <text x="960" y="160" text-anchor="middle" class="subtitle">核心工具：《社区营销活动"五要素"策划模板》</text>
  
  <!-- Central success circle -->
  <g transform="translate(960,500)">
    <circle cx="0" cy="0" r="80" class="center-bg"/>
    <text x="0" y="-10" text-anchor="middle" class="center-text" fill="white">策划</text>
    <text x="0" y="20" text-anchor="middle" class="center-text" fill="white">成功</text>
  </g>
  
  <!-- Element 1: Theme (Top) -->
  <g transform="translate(960,250)">
    <rect x="-150" y="-60" width="300" height="120" rx="15" class="element-bg"/>
    <circle cx="0" cy="-80" r="25" fill="#3498db"/>
    <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">1</text>
    
    <text x="0" y="-25" text-anchor="middle" class="element-title">主题 (Theme)</text>
    <text x="0" y="5" text-anchor="middle" class="element-content">如何一句话吸引客户？</text>
    
    <!-- Theme icon -->
    <g transform="translate(100,-30)">
      <path d="M0,0 L20,0 L15,-8 M20,0 L15,8" fill="none" stroke="#f39c12" stroke-width="3"/>
      <circle cx="-10" cy="0" r="8" fill="#f39c12"/>
    </g>
  </g>
  
  <!-- Element 2: Target (Top Right) -->
  <g transform="translate(1400,350)">
    <rect x="-150" y="-60" width="300" height="120" rx="15" class="element-bg"/>
    <circle cx="0" cy="-80" r="25" fill="#27ae60"/>
    <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">2</text>
    
    <text x="0" y="-25" text-anchor="middle" class="element-title">目标 (Target)</text>
    <text x="0" y="5" text-anchor="middle" class="element-content">我们到底在对谁说话？</text>
    
    <!-- Target icon -->
    <g transform="translate(100,-30)">
      <circle cx="0" cy="0" r="15" fill="none" stroke="#27ae60" stroke-width="3"/>
      <circle cx="0" cy="0" r="8" fill="none" stroke="#27ae60" stroke-width="2"/>
      <circle cx="0" cy="0" r="3" fill="#27ae60"/>
    </g>
  </g>
  
  <!-- Element 3: Process (Bottom Right) -->
  <g transform="translate(1400,650)">
    <rect x="-150" y="-60" width="300" height="120" rx="15" class="element-bg"/>
    <circle cx="0" cy="-80" r="25" fill="#9b59b6"/>
    <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">3</text>
    
    <text x="0" y="-25" text-anchor="middle" class="element-title">流程 (Process)</text>
    <text x="0" y="5" text-anchor="middle" class="element-content">我们的"剧本"是什么？</text>
    
    <!-- Process icon -->
    <g transform="translate(100,-30)">
      <rect x="-10" y="-8" width="20" height="16" rx="3" fill="none" stroke="#9b59b6" stroke-width="2"/>
      <path d="M-5,-3 L5,-3 M-5,0 L5,0 M-5,3 L5,3" stroke="#9b59b6" stroke-width="1"/>
    </g>
  </g>
  
  <!-- Element 4: Checklist (Bottom Left) -->
  <g transform="translate(520,650)">
    <rect x="-150" y="-60" width="300" height="120" rx="15" class="element-bg"/>
    <circle cx="0" cy="-80" r="25" fill="#e67e22"/>
    <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">4</text>
    
    <text x="0" y="-25" text-anchor="middle" class="element-title">物料 (Checklist)</text>
    <text x="0" y="5" text-anchor="middle" class="element-content">我们的"武器弹药"够不够？</text>
    
    <!-- Checklist icon -->
    <g transform="translate(100,-30)">
      <rect x="-8" y="-10" width="16" height="20" rx="2" fill="none" stroke="#e67e22" stroke-width="2"/>
      <path d="M-5,-5 L-2,-2 L5,-8" fill="none" stroke="#e67e22" stroke-width="2"/>
      <path d="M-5,2 L-2,5 L5,-1" fill="none" stroke="#e67e22" stroke-width="2"/>
    </g>
  </g>
  
  <!-- Element 5: KPI (Top Left) -->
  <g transform="translate(520,350)">
    <rect x="-150" y="-60" width="300" height="120" rx="15" class="element-bg"/>
    <circle cx="0" cy="-80" r="25" fill="#e74c3c"/>
    <text x="0" y="-70" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="white">5</text>
    
    <text x="0" y="-25" text-anchor="middle" class="element-title">指标 (KPI)</text>
    <text x="0" y="5" text-anchor="middle" class="element-content">我们如何衡量胜利？</text>
    
    <!-- KPI icon -->
    <g transform="translate(100,-30)">
      <rect x="-8" y="5" width="6" height="-10" fill="#e74c3c"/>
      <rect x="-1" y="5" width="6" height="-15" fill="#e74c3c"/>
      <rect x="6" y="5" width="6" height="-8" fill="#e74c3c"/>
    </g>
  </g>
  
  <!-- Connecting lines -->
  <g stroke="#bdc3c7" stroke-width="2" opacity="0.6">
    <!-- From center to each element -->
    <path d="M960,420 L960,330"/>
    <path d="M1020,460 L1320,390"/>
    <path d="M1020,540 L1320,610"/>
    <path d="M900,540 L600,610"/>
    <path d="M900,460 L600,390"/>
  </g>
  
  <!-- Strategic thinking indicators -->
  <g transform="translate(200,800)">
    <!-- Brain icon -->
    <ellipse cx="25" cy="20" rx="20" ry="15" fill="#3498db" opacity="0.7"/>
    <path d="M10,15 Q15,10 20,15 Q25,10 30,15 Q35,10 40,15" fill="none" stroke="#2c3e50" stroke-width="2"/>
    <text x="60" y="28" font-family="Microsoft YaHei" font-size="24" fill="#3498db">战略思维</text>
  </g>
  
  <g transform="translate(1500,800)">
    <!-- Execution icon -->
    <circle cx="25" cy="20" r="18" fill="none" stroke="#27ae60" stroke-width="3"/>
    <path d="M15,20 L22,27 L35,10" fill="none" stroke="#27ae60" stroke-width="3"/>
    <text x="60" y="28" font-family="Microsoft YaHei" font-size="24" fill="#27ae60">精准执行</text>
  </g>
  
  <!-- Workshop atmosphere -->
  <g transform="translate(100,250)" opacity="0.3">
    <!-- People brainstorming -->
    <g fill="#34495e">
      <circle cx="0" cy="0" r="12"/>
      <circle cx="30" cy="5" r="12"/>
      <circle cx="60" cy="-2" r="12"/>
    </g>
    <!-- Thought bubbles -->
    <circle cx="15" cy="-25" r="8" fill="#ecf0f1" stroke="#bdc3c7"/>
    <circle cx="45" cy="-20" r="6" fill="#ecf0f1" stroke="#bdc3c7"/>
    <circle cx="75" cy="-25" r="8" fill="#ecf0f1" stroke="#bdc3c7"/>
  </g>
  
  <!-- Success formula -->
  <g transform="translate(960,850)">
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#2c3e50">
      完美策划 = 精准定位 + 创意主题 + 流程设计 + 物料准备 + 目标量化
    </text>
  </g>
</svg>
