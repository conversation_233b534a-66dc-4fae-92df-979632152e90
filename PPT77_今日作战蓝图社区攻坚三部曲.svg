<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3498db; }
      .step-subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; fill: #34495e; }
      .time { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #e74c3c; }
      .goal { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; fill: #2c3e50; }
      .step-bg { fill: #f8f9fa; stroke: #3498db; stroke-width: 3; }
      .arrow { fill: #27ae60; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Decorative curves -->
  <path d="M0,200 Q480,150 960,200 T1920,200" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" fill="none" stroke="#ecf0f1" stroke-width="2"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">今日作战蓝图：社区攻坚三部曲</text>
  
  <!-- Step 1: Planning Workshop -->
  <g transform="translate(150,250)">
    <rect x="0" y="0" width="450" height="300" rx="20" class="step-bg"/>
    
    <!-- Step number -->
    <circle cx="60" cy="60" r="35" fill="#3498db"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">1</text>
    
    <!-- Time -->
    <text x="120" y="45" class="time">(09:00-10:00)</text>
    
    <!-- Title -->
    <text x="60" y="120" class="step-title">沙盘推演</text>
    <text x="60" y="155" class="step-subtitle">(Planning Workshop)</text>
    
    <!-- Goal -->
    <text x="30" y="200" class="goal">目标：输出一份专业的</text>
    <text x="30" y="230" class="goal">《社区活动策划书》。</text>
    
    <!-- Icon -->
    <g transform="translate(300,80)">
      <!-- Planning document -->
      <rect x="0" y="0" width="80" height="100" rx="5" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
      <rect x="10" y="15" width="60" height="5" fill="#3498db"/>
      <rect x="10" y="30" width="50" height="3" fill="#95a5a6"/>
      <rect x="10" y="40" width="55" height="3" fill="#95a5a6"/>
      <rect x="10" y="50" width="45" height="3" fill="#95a5a6"/>
      <rect x="10" y="65" width="60" height="5" fill="#e74c3c"/>
      <rect x="10" y="80" width="40" height="3" fill="#95a5a6"/>
    </g>
  </g>
  
  <!-- Arrow 1 -->
  <g transform="translate(620,400)">
    <path d="M0,0 L80,0 M65,-15 L80,0 L65,15" fill="none" stroke="#27ae60" stroke-width="4"/>
  </g>
  
  <!-- Step 2: On-site Execution -->
  <g transform="translate(735,250)">
    <rect x="0" y="0" width="450" height="300" rx="20" class="step-bg"/>
    
    <!-- Step number -->
    <circle cx="60" cy="60" r="35" fill="#27ae60"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">2</text>
    
    <!-- Time -->
    <text x="120" y="45" class="time">(10:00-15:00)</text>
    
    <!-- Title -->
    <text x="60" y="120" class="step-title">雷霆出击</text>
    <text x="60" y="155" class="step-subtitle">(On-site Execution)</text>
    
    <!-- Goal -->
    <text x="30" y="200" class="goal">目标：在真实社区，</text>
    <text x="30" y="230" class="goal">批量获取有效潜客线索。</text>
    
    <!-- Icon -->
    <g transform="translate(300,80)">
      <!-- Community building -->
      <rect x="10" y="40" width="60" height="60" fill="#34495e"/>
      <rect x="20" y="50" width="15" height="15" fill="#f1c40f"/>
      <rect x="45" y="50" width="15" height="15" fill="#f1c40f"/>
      <rect x="20" y="75" width="15" height="15" fill="#f1c40f"/>
      <rect x="45" y="75" width="15" height="15" fill="#f1c40f"/>
      <!-- People -->
      <circle cx="0" cy="90" r="8" fill="#e74c3c"/>
      <circle cx="90" cy="85" r="8" fill="#e74c3c"/>
    </g>
  </g>
  
  <!-- Arrow 2 -->
  <g transform="translate(1205,400)">
    <path d="M0,0 L80,0 M65,-15 L80,0 L65,15" fill="none" stroke="#27ae60" stroke-width="4"/>
  </g>
  
  <!-- Step 3: Results Debrief -->
  <g transform="translate(1320,250)">
    <rect x="0" y="0" width="450" height="300" rx="20" class="step-bg"/>
    
    <!-- Step number -->
    <circle cx="60" cy="60" r="35" fill="#e74c3c"/>
    <text x="60" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="white">3</text>
    
    <!-- Time -->
    <text x="120" y="45" class="time">(15:00-17:00)</text>
    
    <!-- Title -->
    <text x="60" y="120" class="step-title">战果收割</text>
    <text x="60" y="155" class="step-subtitle">(Results Debrief)</text>
    
    <!-- Goal -->
    <text x="30" y="200" class="goal">目标：线索分级，锁定A类客户，</text>
    <text x="30" y="230" class="goal">并为明日决战铺路。</text>
    
    <!-- Icon -->
    <g transform="translate(300,80)">
      <!-- Trophy -->
      <rect x="25" y="60" width="30" height="40" fill="#f1c40f"/>
      <ellipse cx="40" cy="60" rx="20" ry="8" fill="#f39c12"/>
      <rect x="35" y="100" width="10" height="20" fill="#8b4513"/>
      <rect x="20" y="120" width="40" height="10" fill="#8b4513"/>
      <!-- Stars -->
      <path d="M10,30 L15,20 L20,30 L30,30 L22,38 L25,48 L15,42 L5,48 L8,38 L0,30 Z" fill="#f1c40f"/>
      <path d="M60,25 L63,18 L66,25 L73,25 L68,30 L70,37 L63,33 L56,37 L58,30 L53,25 Z" fill="#f1c40f"/>
    </g>
  </g>
  
  <!-- Bottom emphasis -->
  <g transform="translate(0,650)">
    <!-- Connecting flow line -->
    <path d="M375,0 Q960,-50 1545,0" fill="none" stroke="#3498db" stroke-width="4" opacity="0.6"/>
    
    <!-- Key message -->
    <rect x="660" y="50" width="600" height="100" rx="20" fill="#fff" stroke="#e74c3c" stroke-width="3"/>
    <text x="960" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#e74c3c">每一步都至关重要！</text>
    <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#2c3e50">精心策划 → 全力执行 → 精准收割</text>
  </g>
  
  <!-- Time indicators -->
  <g transform="translate(100,750)">
    <!-- Clock -->
    <circle cx="30" cy="30" r="25" fill="none" stroke="#3498db" stroke-width="3"/>
    <path d="M30,10 L30,30 L45,30" stroke="#3498db" stroke-width="3"/>
    <text x="70" y="38" font-family="Microsoft YaHei" font-size="24" fill="#3498db">总计8小时</text>
  </g>
  
  <g transform="translate(1650,750)">
    <!-- Target -->
    <circle cx="30" cy="30" r="25" fill="none" stroke="#e74c3c" stroke-width="3"/>
    <circle cx="30" cy="30" r="15" fill="none" stroke="#e74c3c" stroke-width="2"/>
    <circle cx="30" cy="30" r="8" fill="#e74c3c"/>
    <text x="70" y="38" font-family="Microsoft YaHei" font-size="24" fill="#e74c3c">决战准备</text>
  </g>
</svg>
