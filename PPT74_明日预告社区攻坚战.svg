<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #2c3e50; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #e74c3c; }
      .subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #34495e; }
      .keyword { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3498db; }
      .separator { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #95a5a6; }
      .community-bg { fill: #ecf0f1; opacity: 0.3; }
    </style>
    
    <!-- Gradient for background -->
    <linearGradient id="communityGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#2c3e50;stop-opacity:0.1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- Community background illustration -->
  <rect x="0" y="200" width="1920" height="600" fill="url(#communityGrad)"/>
  
  <!-- Community buildings silhouette -->
  <g transform="translate(200,300)" opacity="0.2">
    <!-- Buildings -->
    <rect x="0" y="200" width="80" height="300" fill="#34495e"/>
    <rect x="100" y="150" width="100" height="350" fill="#34495e"/>
    <rect x="220" y="180" width="90" height="320" fill="#34495e"/>
    <rect x="330" y="120" width="110" height="380" fill="#34495e"/>
    <rect x="460" y="160" width="85" height="340" fill="#34495e"/>
    <rect x="570" y="140" width="95" height="360" fill="#34495e"/>
    <rect x="690" y="190" width="100" height="310" fill="#34495e"/>
    <rect x="810" y="170" width="90" height="330" fill="#34495e"/>
    <rect x="920" y="130" width="105" height="370" fill="#34495e"/>
    <rect x="1050" y="200" width="80" height="300" fill="#34495e"/>
    
    <!-- Windows -->
    <g fill="#f1c40f" opacity="0.6">
      <rect x="20" y="220" width="15" height="20"/>
      <rect x="45" y="240" width="15" height="20"/>
      <rect x="20" y="280" width="15" height="20"/>
      <rect x="130" y="180" width="15" height="20"/>
      <rect x="155" y="200" width="15" height="20"/>
      <rect x="130" y="240" width="15" height="20"/>
      <rect x="250" y="210" width="15" height="20"/>
      <rect x="275" y="230" width="15" height="20"/>
    </g>
  </g>
  
  <!-- Decorative curves -->
  <path d="M0,150 Q480,100 960,150 T1920,150" fill="none" stroke="#3498db" stroke-width="3" opacity="0.5"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" fill="none" stroke="#3498db" stroke-width="3" opacity="0.5"/>
  
  <!-- Title -->
  <text x="960" y="120" text-anchor="middle" class="title">明日预告：战场转移，难度升级！</text>
  
  <!-- Main content area -->
  <g transform="translate(0,350)">
    <!-- Day 3 announcement -->
    <text x="960" y="100" text-anchor="middle" class="day-title">Day 3: 社区攻坚战</text>
    
    <!-- Keywords -->
    <g transform="translate(0,200)">
      <text x="480" y="0" text-anchor="middle" class="keyword">主动出击</text>
      <text x="720" y="0" text-anchor="middle" class="separator">|</text>
      <text x="960" y="0" text-anchor="middle" class="keyword">团队作战</text>
      <text x="1200" y="0" text-anchor="middle" class="separator">|</text>
      <text x="1440" y="0" text-anchor="middle" class="keyword">批量获客</text>
    </g>
  </g>
  
  <!-- Marketing team illustration -->
  <g transform="translate(300,650)" opacity="0.4">
    <!-- Exhibition table -->
    <rect x="200" y="100" width="300" height="80" rx="10" fill="#8b4513"/>
    <rect x="180" y="180" width="20" height="60" fill="#654321"/>
    <rect x="520" y="180" width="20" height="60" fill="#654321"/>
    
    <!-- Banner -->
    <rect x="150" y="50" width="400" height="60" rx="5" fill="#3498db"/>
    <text x="350" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="white">联通智家通通</text>
    
    <!-- People silhouettes -->
    <g fill="#2c3e50">
      <!-- Person 1 -->
      <circle cx="100" cy="150" r="20"/>
      <rect x="90" y="170" width="20" height="40"/>
      <rect x="85" y="180" width="10" height="30"/>
      <rect x="105" y="180" width="10" height="30"/>
      
      <!-- Person 2 -->
      <circle cx="350" cy="140" r="20"/>
      <rect x="340" y="160" width="20" height="40"/>
      <rect x="335" y="170" width="10" height="30"/>
      <rect x="355" y="170" width="10" height="30"/>
      
      <!-- Person 3 -->
      <circle cx="600" cy="155" r="20"/>
      <rect x="590" y="175" width="20" height="40"/>
      <rect x="585" y="185" width="10" height="30"/>
      <rect x="605" y="185" width="10" height="30"/>
    </g>
    
    <!-- Residents -->
    <g fill="#34495e" opacity="0.6">
      <!-- Resident 1 -->
      <circle cx="50" cy="200" r="15"/>
      <rect x="42" y="215" width="16" height="35"/>
      
      <!-- Resident 2 -->
      <circle cx="650" cy="190" r="15"/>
      <rect x="642" y="205" width="16" height="35"/>
      
      <!-- Resident 3 -->
      <circle cx="400" cy="220" r="15"/>
      <rect x="392" y="235" width="16" height="35"/>
    </g>
  </g>
  
  <!-- Motivational elements -->
  <g transform="translate(100,950)">
    <!-- Arrow pointing forward -->
    <path d="M0,0 L40,0 L30,-10 M40,0 L30,10" fill="none" stroke="#e74c3c" stroke-width="4"/>
    <text x="60" y="8" font-family="Microsoft YaHei" font-size="28" fill="#e74c3c" font-weight="bold">王牌，从不等待！</text>
  </g>
  
  <g transform="translate(1400,950)">
    <!-- Target icon -->
    <circle cx="20" cy="0" r="18" fill="none" stroke="#27ae60" stroke-width="3"/>
    <circle cx="20" cy="0" r="12" fill="none" stroke="#27ae60" stroke-width="2"/>
    <circle cx="20" cy="0" r="6" fill="#27ae60"/>
    <text x="60" y="8" font-family="Microsoft YaHei" font-size="28" fill="#27ae60" font-weight="bold">主动出击，创造机会！</text>
  </g>
</svg>
