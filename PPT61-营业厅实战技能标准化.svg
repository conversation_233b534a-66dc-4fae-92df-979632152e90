<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#ffffff"/>
  
  <!-- 营业厅背景图效果 -->
  <defs>
    <linearGradient id="hallBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:0.3"/>
      <stop offset="50%" style="stop-color:#e9ecef;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#dee2e6;stop-opacity:0.7"/>
    </linearGradient>
    
    <!-- 弧线装饰元素 -->
    <path id="arcDecor" d="M 0,200 Q 400,100 800,200 T 1600,200" stroke="#0066cc" stroke-width="3" fill="none" opacity="0.3"/>
  </defs>
  
  <!-- 背景装饰弧线 -->
  <use href="#arcDecor" transform="translate(160, 100)"/>
  <use href="#arcDecor" transform="translate(160, 800) scale(1, -1)"/>
  
  <!-- 营业厅背景效果 -->
  <rect x="0" y="0" width="1920" height="1080" fill="url(#hallBg)"/>
  
  <!-- 智家体验区标识 -->
  <rect x="1400" y="150" width="400" height="120" rx="20" fill="#0066cc" opacity="0.1"/>
  <text x="1600" y="190" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#0066cc" text-anchor="middle">智家体验区</text>
  <text x="1600" y="220" font-family="Microsoft YaHei" font-size="18" fill="#0066cc" text-anchor="middle">Smart Home Experience</text>
  <text x="1600" y="245" font-family="Microsoft YaHei" font-size="18" fill="#0066cc" text-anchor="middle">Zone</text>
  
  <!-- 主标题 -->
  <text x="960" y="450" font-family="Microsoft YaHei" font-size="88" font-weight="bold" fill="#1a1a1a" text-anchor="middle">第二天：营业厅实战 · 技能标准化</text>
  
  <!-- 副标题 -->
  <text x="960" y="550" font-family="Microsoft YaHei" font-size="48" font-weight="300" fill="#333333" text-anchor="middle">从"知道"到"做到"的唯一路径是"重复"</text>
  
  <!-- 装饰弧线 -->
  <path d="M 300,600 Q 960,650 1620,600" stroke="#0066cc" stroke-width="4" fill="none" opacity="0.6"/>
  
  <!-- 底部英文标题 -->
  <text x="960" y="950" font-family="Microsoft YaHei" font-size="32" fill="#666666" text-anchor="middle">Day 2: Retail Store Practice · Skill Standardization</text>
</svg>
